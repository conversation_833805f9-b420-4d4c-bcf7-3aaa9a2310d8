import apiClient from "@/apiClient";
import type { APGetCustomFieldType, GetCCCustomField } from "@/type";
import matchString from "@/utils/matchString";
import { getBidirectionalMapping } from "./mapping";
/**
 * Custom Fields Processor
 *
 * Simple processor that returns empty JSON response.
 */

export async function synchronizeCustomFields(): Promise<{}> {
	const apCustomFields = await apiClient.ap.apCustomfield.allWithParentFilter();
	const ccCustomFields = await apiClient.cc.ccCustomfieldReq.all();

	const matchFields = [];

	ccCustomFields.forEach((ccf) => {
		findAPField(ccf, apCustomFields);
	});

	return {
		apCustomFields,
		ccCustomFields,
	};
}

const findAPField = (
	ccField: GetCCCustomField,
	apCustomFields: APGetCustomFieldType[],
) => {
	const type = getBidirectionalMapping(ccField);
	const apField = apCustomFields.find((apf) => {
		const apName = matchString(apf.name, ccField.name);
		const apLabel = matchString(apf.name, ccField.label);
		const keyword = matchString(apf.fieldKey?.split(".")?.pop(), ccField.name);
		const apType = type.ccToAP === apf.dataType;
		return (apName || apLabel || keyword) && apType;
	});
	if (!apField) {
		const { name, label, allowMultipleValues, allowedValues, ...restCC } =
			ccField;
		console.log({
			ccField: {
				name,
				label,
				allowMultipleValues,
				allowedValues: JSON.stringify(allowedValues.map((v) => v.value)),
				type: ccField.type,
			},
			type,
		});
		console.log("-------------------------");
		return;
	}
	// console.log({ccField: ccField.name, apField});
};
